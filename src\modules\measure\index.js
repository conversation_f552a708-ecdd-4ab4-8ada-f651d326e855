/**
 * 测量模块入口文件
 * 统一导出测量相关的所有功能
 */

import { MeasureModule } from './MeasureModule.js';
import { MeasureUI } from './MeasureUI.js';

/**
 * 测量工具管理器
 * 整合测量逻辑和UI界面
 */
export class MeasureManager {
    constructor(viewer, container) {
        this.viewer = viewer;
        this.container = container;
        this.measureModule = null;
        this.measureUI = null;
        this.isInitialized = false;
    }

    /**
     * 初始化测量工具
     */
    async init() {
        if (this.isInitialized) {
            console.warn('测量工具已经初始化');
            return;
        }

        try {
            // 初始化测量模块
            this.measureModule = new MeasureModule(this.viewer);
            
            // 初始化UI界面
            this.measureUI = new MeasureUI(this.container, this.measureModule);
            
            this.isInitialized = true;
            console.log('✅ 测量工具初始化成功');
            
            return this;
        } catch (error) {
            console.error('❌ 测量工具初始化失败:', error);
            throw error;
        }
    }

    /**
     * 激活测量工具
     */
    activate() {
        if (!this.isInitialized) {
            console.warn('测量工具未初始化');
            return;
        }

        // 显示容器
        if (this.container) {
            this.container.style.display = 'block';
        }

        console.log('📏 测量工具已激活');
    }

    /**
     * 禁用测量工具
     */
    disable() {
        if (!this.isInitialized) {
            return;
        }

        // 取消当前绘制
        if (this.measureModule) {
            this.measureModule.cancelCurrentDrawing();
        }

        // 隐藏容器
        if (this.container) {
            this.container.style.display = 'none';
        }

        console.log('📏 测量工具已禁用');
    }

    /**
     * 清除所有测量结果
     */
    clearAll() {
        if (this.measureModule) {
            this.measureModule.clearAll();
        }
        
        if (this.measureUI) {
            this.measureUI.clearAllMeasurements();
        }
    }

    /**
     * 获取测量模块
     */
    getMeasureModule() {
        return this.measureModule;
    }

    /**
     * 获取UI模块
     */
    getUI() {
        return this.measureUI;
    }

    /**
     * 销毁测量工具
     */
    destroy() {
        if (this.measureModule) {
            this.measureModule.destroy();
            this.measureModule = null;
        }

        if (this.measureUI) {
            this.measureUI.destroy();
            this.measureUI = null;
        }

        this.isInitialized = false;
        console.log('📏 测量工具已销毁');
    }
}

/**
 * 创建测量工具实例的工厂函数
 * @param {Cesium.Viewer} viewer Cesium视图器
 * @param {HTMLElement} container 容器元素
 * @returns {Promise<MeasureManager>} 测量工具管理器实例
 */
export async function createMeasureTool(viewer, container) {
    const manager = new MeasureManager(viewer, container);
    await manager.init();
    return manager;
}

// 导出核心类
export { MeasureModule, MeasureUI };

// 默认导出
export default MeasureManager;
