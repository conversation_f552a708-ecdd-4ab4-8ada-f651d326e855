/* 🌟 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #1a1a1a;
    height: 100vh;
    width: 100vw;
}

/* 🌍 Cesium容器 */
#cesiumContainer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
}


.notification {
    background: rgba(30, 30, 30, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 16px 20px;
    margin-bottom: 12px;
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    color: #ffffff;
    font-size: 14px;
    line-height: 1.4;
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-color: rgba(76, 175, 80, 0.5);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.notification.error {
    border-color: rgba(244, 67, 54, 0.5);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.notification.warning {
    border-color: rgba(255, 193, 7, 0.5);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.notification-title {
    font-weight: 600;
    margin-bottom: 4px;
    color: #40E0FF;
}

.notification.hide {
    transform: translateX(100%);
    opacity: 0;
}

/* 🎯 SVG容器 */
#svg-container {
    display: none;
}

.toolbar-container {
    position: fixed;
    right: 10px;
    bottom: 30px;
    display: flex;
    flex-direction: column;
    gap: 6px;
    z-index: 2000;
}

.toolbar-button {
    width: 36px;
    height: 36px;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    backdrop-filter: blur(20px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    color: #ffffff;
    font-size: 14px;
}

.toolbar-button:hover {
    background: rgba(0, 0, 0, 0.9);
    border-color: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    color: #fff;
}

.toolbar-button:active {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.toolbar-button.active {
    background: rgba(64, 224, 255, 0.2);
    border-color: rgba(64, 224, 255, 0.8);
    color: #40E0FF;
}

/* 🔧 工具栏弹窗样式 */
.toolbar-popup {
    position: absolute;
    right: 50px; /* 按钮宽度36px + 间距14px */
    bottom: 0; /* 改为底边对齐 */
    width: 320px;
    height: 400px;
    background: rgba(20, 20, 30, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    z-index: 1999;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    overflow: hidden;
}

.toolbar-popup.show {
    opacity: 1;
    transform: translateX(0);
    pointer-events: all;
}

.toolbar-popup::before {
    content: '';
    position: absolute;
    bottom: 18px; /* 按钮高度36px的一半 */
    right: -8px;
    width: 0;
    height: 0;
    border-left: 8px solid rgba(20, 20, 30, 0.95);
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
}

.popup-header {
    padding: 10px 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(64, 224, 255, 0.05);
}

.popup-title {
    font-size: 14px;
    font-weight: 500;
    color: #40E0FF;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 6px;
}

.popup-content {
    padding: 16px;
    height: calc(100% - 46px);
    overflow-y: auto;
    color: #ffffff;
}

.popup-content::-webkit-scrollbar {
    width: 6px;
}

.popup-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.popup-content::-webkit-scrollbar-thumb {
    background: rgba(64, 224, 255, 0.5);
    border-radius: 3px;
}

.popup-content::-webkit-scrollbar-thumb:hover {
    background: rgba(64, 224, 255, 0.7);
}

/* 工具栏按钮容器相对定位 */
.toolbar-container {
    position: fixed;
    right: 10px;
    bottom: 30px;
    display: flex;
    flex-direction: column;
    gap: 6px;
    z-index: 2000;
}

.toolbar-item {
    position: relative;
}