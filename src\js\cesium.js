// 初始化Cesium函数
function initCesium() {
    // 设置访问令牌
    Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJhM2Y5ZDQxYy0wNWEwLTRkZTAtODI0Mi0wNmQ4MDg1ZTQxZjQiLCJpZCI6MTM4NDM4LCJpYXQiOjE2ODM5NzY5NjV9.nOFHD37Doch8oxs9jkjDJvOe_3yOeypDjxNQpIaZW2U';

    // 初始化 Viewer
    const viewer = new Cesium.Viewer('cesiumContainer', {
        animation: false,
        baseLayerPicker: false,
        fullscreenButton: false,
        vrButton: false,
        geocoder: false,
        homeButton: false,
        infoBox: false,
        sceneModePicker: false,
        selectionIndicator: false,
        timeline: false,
        navigationHelpButton: false,
        terrain: Cesium.Terrain.fromWorldTerrain({
            requestWaterMask: true,  // 启用水面效果
            requestVertexNormals: true  // 启用地形光照
        })
    });
    
    // 将viewer设置为全局变量
    window.viewer = viewer;

    // 移除默认的影像图层
    viewer.imageryLayers.removeAll();

    // 添加谷歌卫星影像图层
    const googleProvider = new Cesium.UrlTemplateImageryProvider({
        url: 'https://mt{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
        subdomains: ['0', '1', '2', '3'],
        minimumLevel: 1,
        maximumLevel: 22,
        credit: 'Google Earth',
        customTags: {
            s: function() {
                return Math.floor(Math.random() * 4).toString();
            }
        }
    });

    viewer.imageryLayers.addImageryProvider(googleProvider);

    // 移除黑色背景
    viewer.scene.globe.baseColor = Cesium.Color.WHITE;
    
    // 基础设置
    viewer.scene.globe.enableLighting = false;  // 启用全球光照
    viewer.scene.skyAtmosphere.show = true;
    viewer.scene.fog.enabled = true;
    viewer.scene.globe.showGroundAtmosphere = true;
    
    // 相机设置
    viewer.scene.screenSpaceCameraController.minimumZoomDistance = 100;
    viewer.scene.screenSpaceCameraController.maximumZoomDistance = 18000000;
    viewer.scene.screenSpaceCameraController.enableTilt = true;  // 启用倾斜，以便更好地观察地形

    // 初始位置
    viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(104.0, 30.0, 17000000),
        orientation: {
            heading: 0.0,
            pitch: -Cesium.Math.PI_OVER_TWO,
            roll: 0.0
        }
    });

    // 限制相机视角范围
    viewer.scene.screenSpaceCameraController.minimumZoomRate = 300;
    viewer.scene.screenSpaceCameraController.maximumZoomRate = 5.0e6;
    viewer.scene.screenSpaceCameraController._maximumMagnitude = 18000000;

    // 添加瓦片加载事件监听
    viewer.scene.globe.tileLoadProgressEvent.addEventListener(function(queuedTileCount) {
        // console.log('正在加载瓦片数量:', queuedTileCount);
    });

    // 开启地形深度测试，提高瓦片显示质量
    viewer.scene.globe.depthTestAgainstTerrain = true;

    // 优化性能
    viewer.scene.globe.maximumScreenSpaceError = 2.0;
    viewer.scene.globe.tileCacheSize = 1000;

    // 移除版权信息
    viewer._cesiumWidget._creditContainer.style.display = "none";

    // 初始化导航控件
    const options = {
        enableCompass: true,
        enableZoomControls: true,
        enableDistanceLegend: true,
        enableCompassOuterRing: true
    };
    new CesiumNavigation(viewer, options);



    // 设置场景模式为3D
    viewer.scene.mode = Cesium.SceneMode.SCENE3D;
    
    // 启用批量渲染
    if (Cesium.FeatureDetection.supportsRenderingUint8ArraysToFloatTextures) {
        viewer.scene.context.uniformState.batchTable.featuresLength = 0;
    }
    
    // 配置性能参数
    viewer.scene.logarithmicDepthBuffer = true;
    viewer.scene.requestRenderMode = true
    viewer.scene.maximumRenderTimeChange = 0.0;
    viewer.scene.debugShowFramesPerSecond = false;

    // 添加调试信息
    console.log('Cesium 初始化成功！');

    // 初始化天空盒
    const skyBox = SkyBoxManager.initDefaultSkyBox(viewer);
    
    let aaa;

    // 定义函数并添加到window对象使其全局可用
    window.fly = function() {
        viewer.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(98.71707797694049, 27.677299704639537, 50000.0)
        });
    };
    
    window.clea = function() {
        aaa.clear();
    };

    return viewer;
}

// 导出初始化函数
window.initCesium = initCesium;
