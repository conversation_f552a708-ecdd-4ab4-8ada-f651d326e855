/**
 * 测量分析模块 - ES6模块化设计
 * 基于Cesium的现代化测量工具
 */

export class MeasureModule {
    constructor(viewer) {
        this.viewer = viewer;
        this.measureEntities = [];
        this.activeDrawing = null;
        this.measureResults = new Map();
        this.handlers = new Map();
        
        this.init();
    }

    /**
     * 初始化模块
     */
    init() {
        this.createMeasureDataSource();
        this.setupEventHandlers();
    }

    /**
     * 创建测量数据源
     */
    createMeasureDataSource() {
        this.measureDataSource = new Cesium.CustomDataSource('measure-results');
        this.viewer.dataSources.add(this.measureDataSource);
    }

    /**
     * 设置事件处理器
     */
    setupEventHandlers() {
        // 键盘事件 - ESC取消当前绘制
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.activeDrawing) {
                this.cancelCurrentDrawing();
            }
        });
    }

    /**
     * 测量距离
     * @param {Object} options 配置选项
     */
    async measureDistance(options = {}) {
        const config = {
            clampToGround: options.clampToGround || false,
            unit: options.unit || 'auto',
            color: options.color || Cesium.Color.YELLOW,
            ...options
        };

        return new Promise((resolve, reject) => {
            this.cancelCurrentDrawing();
            
            const positions = [];
            const dynamicPositions = new Cesium.CallbackProperty(() => positions, false);
            
            // 创建动态线条
            const polylineEntity = this.measureDataSource.entities.add({
                polyline: {
                    positions: dynamicPositions,
                    clampToGround: config.clampToGround,
                    width: 3,
                    material: config.color,
                    depthFailMaterial: config.color
                }
            });

            // 创建点击处理器
            const handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
            
            handler.setInputAction((click) => {
                const position = this.viewer.camera.pickEllipsoid(click.position, this.viewer.scene.globe.ellipsoid);
                if (!position) return;

                positions.push(position);
                
                // 添加点标记
                this.addMeasurePoint(position, positions.length);

                // 如果有两个点，完成测量
                if (positions.length >= 2) {
                    const distance = this.calculateDistance(positions, config.clampToGround);
                    const formattedDistance = this.formatDistance(distance, config.unit);
                    
                    // 添加距离标签
                    this.addDistanceLabel(positions, formattedDistance);
                    
                    // 清理处理器
                    handler.destroy();
                    this.activeDrawing = null;
                    
                    resolve({
                        type: 'distance',
                        positions: positions,
                        distance: distance,
                        formattedDistance: formattedDistance,
                        entity: polylineEntity
                    });
                }
            }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

            // 鼠标移动动态显示
            handler.setInputAction((movement) => {
                if (positions.length === 0) return;
                
                const position = this.viewer.camera.pickEllipsoid(movement.endPosition, this.viewer.scene.globe.ellipsoid);
                if (!position) return;

                const dynamicPositions = [...positions, position];
                polylineEntity.polyline.positions = dynamicPositions;
                
                if (positions.length >= 1) {
                    const distance = this.calculateDistance(dynamicPositions, config.clampToGround);
                    const formattedDistance = this.formatDistance(distance, config.unit);
                    this.updateTooltip(movement.endPosition, `距离: ${formattedDistance}`);
                }
            }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

            this.activeDrawing = { handler, entity: polylineEntity, type: 'distance' };
        });
    }

    /**
     * 测量面积
     * @param {Object} options 配置选项
     */
    async measureArea(options = {}) {
        const config = {
            clampToGround: options.clampToGround || false,
            unit: options.unit || 'auto',
            color: options.color || Cesium.Color.CYAN.withAlpha(0.5),
            outlineColor: options.outlineColor || Cesium.Color.CYAN,
            ...options
        };

        return new Promise((resolve, reject) => {
            this.cancelCurrentDrawing();
            
            const positions = [];
            const dynamicPositions = new Cesium.CallbackProperty(() => positions, false);
            
            // 创建动态多边形
            const polygonEntity = this.measureDataSource.entities.add({
                polygon: {
                    hierarchy: dynamicPositions,
                    material: config.color,
                    outline: true,
                    outlineColor: config.outlineColor,
                    height: config.clampToGround ? 0 : undefined,
                    heightReference: config.clampToGround ? Cesium.HeightReference.CLAMP_TO_GROUND : undefined
                }
            });

            const handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
            
            handler.setInputAction((click) => {
                const position = this.viewer.camera.pickEllipsoid(click.position, this.viewer.scene.globe.ellipsoid);
                if (!position) return;

                positions.push(position);
                this.addMeasurePoint(position, positions.length);

            }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

            // 双击完成
            handler.setInputAction((click) => {
                if (positions.length < 3) {
                    this.showNotification('提示', '至少需要3个点才能形成面积', 'warning');
                    return;
                }

                const area = this.calculateArea(positions);
                const formattedArea = this.formatArea(area, config.unit);
                
                // 添加面积标签
                this.addAreaLabel(positions, formattedArea);
                
                handler.destroy();
                this.activeDrawing = null;
                
                resolve({
                    type: 'area',
                    positions: positions,
                    area: area,
                    formattedArea: formattedArea,
                    entity: polygonEntity
                });
            }, Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);

            // 鼠标移动
            handler.setInputAction((movement) => {
                if (positions.length === 0) return;
                
                const position = this.viewer.camera.pickEllipsoid(movement.endPosition, this.viewer.scene.globe.ellipsoid);
                if (!position) return;

                const dynamicPositions = [...positions, position];
                polygonEntity.polygon.hierarchy = dynamicPositions;
                
                if (positions.length >= 2) {
                    const area = this.calculateArea(dynamicPositions);
                    const formattedArea = this.formatArea(area, config.unit);
                    this.updateTooltip(movement.endPosition, `面积: ${formattedArea}`);
                }
            }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

            this.activeDrawing = { handler, entity: polygonEntity, type: 'area' };
        });
    }

    /**
     * 测量高度差
     * @param {Object} options 配置选项
     */
    async measureHeight(options = {}) {
        const config = {
            color: options.color || Cesium.Color.RED,
            ...options
        };

        return new Promise((resolve, reject) => {
            this.cancelCurrentDrawing();
            
            const positions = [];
            let startPoint = null;
            let endPoint = null;

            const handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
            
            handler.setInputAction((click) => {
                const position = this.viewer.camera.pickEllipsoid(click.position, this.viewer.scene.globe.ellipsoid);
                if (!position) return;

                positions.push(position);

                if (positions.length === 1) {
                    startPoint = this.addMeasurePoint(position, 1, '起点');
                } else if (positions.length === 2) {
                    endPoint = this.addMeasurePoint(position, 2, '终点');
                    
                    // 计算高度差
                    const heightDiff = this.calculateHeightDifference(positions[0], positions[1]);
                    const distance = this.calculateDistance(positions, false);
                    
                    // 添加连接线和标签
                    this.addHeightLine(positions, heightDiff, distance);
                    
                    handler.destroy();
                    this.activeDrawing = null;
                    
                    resolve({
                        type: 'height',
                        positions: positions,
                        heightDifference: heightDiff,
                        distance: distance,
                        startPoint: startPoint,
                        endPoint: endPoint
                    });
                }
            }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

            this.activeDrawing = { handler, type: 'height' };
        });
    }

    /**
     * 计算距离
     */
    calculateDistance(positions, clampToGround = false) {
        if (positions.length < 2) return 0;

        let distance = 0;
        for (let i = 0; i < positions.length - 1; i++) {
            if (clampToGround) {
                // 贴地距离计算
                distance += this.calculateGroundDistance(positions[i], positions[i + 1]);
            } else {
                // 空间直线距离
                distance += Cesium.Cartesian3.distance(positions[i], positions[i + 1]);
            }
        }
        return distance;
    }

    /**
     * 计算贴地距离
     */
    calculateGroundDistance(position1, position2) {
        const cartographic1 = Cesium.Cartographic.fromCartesian(position1);
        const cartographic2 = Cesium.Cartographic.fromCartesian(position2);

        // 使用椭球面距离计算
        const geodesic = new Cesium.EllipsoidGeodesic(cartographic1, cartographic2);
        return geodesic.surfaceDistance;
    }

    /**
     * 计算面积
     */
    calculateArea(positions) {
        if (positions.length < 3) return 0;
        
        // 转换为地理坐标
        const coords = positions.map(pos => {
            const cartographic = Cesium.Cartographic.fromCartesian(pos);
            return [
                Cesium.Math.toDegrees(cartographic.longitude),
                Cesium.Math.toDegrees(cartographic.latitude)
            ];
        });
        
        // 使用turf.js计算面积
        if (window.turf) {
            const polygon = window.turf.polygon([coords.concat([coords[0]])]);
            return window.turf.area(polygon);
        }
        
        // 备用计算方法
        return this.calculatePolygonArea(positions);
    }

    /**
     * 格式化距离显示
     */
    formatDistance(distance, unit = 'auto') {
        if (unit === 'auto') {
            if (distance < 1000) {
                return `${distance.toFixed(2)} 米`;
            } else {
                return `${(distance / 1000).toFixed(2)} 公里`;
            }
        }
        
        const units = {
            'm': { factor: 1, label: '米' },
            'km': { factor: 1000, label: '公里' },
            'mile': { factor: 1852, label: '海里' }
        };
        
        const unitConfig = units[unit] || units.m;
        return `${(distance / unitConfig.factor).toFixed(2)} ${unitConfig.label}`;
    }

    /**
     * 格式化面积显示
     */
    formatArea(area, unit = 'auto') {
        if (unit === 'auto') {
            if (area < 10000) {
                return `${area.toFixed(2)} 平方米`;
            } else {
                return `${(area / 1000000).toFixed(2)} 平方公里`;
            }
        }
        
        const units = {
            'm': { factor: 1, label: '平方米' },
            'km': { factor: 1000000, label: '平方公里' },
            'ha': { factor: 10000, label: '公顷' },
            'mu': { factor: 666.67, label: '亩' }
        };
        
        const unitConfig = units[unit] || units.m;
        return `${(area / unitConfig.factor).toFixed(2)} ${unitConfig.label}`;
    }

    /**
     * 清除所有测量结果
     */
    clearAll() {
        this.cancelCurrentDrawing();
        this.measureDataSource.entities.removeAll();
        this.measureResults.clear();
        this.hideTooltip();
    }

    /**
     * 取消当前绘制
     */
    cancelCurrentDrawing() {
        if (this.activeDrawing) {
            if (this.activeDrawing.handler) {
                this.activeDrawing.handler.destroy();
            }
            if (this.activeDrawing.entity) {
                this.measureDataSource.entities.remove(this.activeDrawing.entity);
            }
            this.activeDrawing = null;
        }
    }

    /**
     * 添加测量点标记
     */
    addMeasurePoint(position, index, label = null) {
        const pointEntity = this.measureDataSource.entities.add({
            position: position,
            point: {
                pixelSize: 8,
                color: Cesium.Color.YELLOW,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                disableDepthTestDistance: Number.POSITIVE_INFINITY
            },
            label: {
                text: label || index.toString(),
                font: '12pt sans-serif',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                pixelOffset: new Cesium.Cartesian2(0, -40),
                disableDepthTestDistance: Number.POSITIVE_INFINITY
            }
        });
        return pointEntity;
    }

    /**
     * 添加距离标签
     */
    addDistanceLabel(positions, formattedDistance) {
        const midpoint = Cesium.Cartesian3.midpoint(positions[0], positions[1], new Cesium.Cartesian3());

        this.measureDataSource.entities.add({
            position: midpoint,
            label: {
                text: formattedDistance,
                font: '14pt sans-serif',
                fillColor: Cesium.Color.YELLOW,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                pixelOffset: new Cesium.Cartesian2(0, -20),
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
                backgroundColor: Cesium.Color.BLACK.withAlpha(0.7),
                backgroundPadding: new Cesium.Cartesian2(8, 4)
            }
        });
    }

    /**
     * 添加面积标签
     */
    addAreaLabel(positions, formattedArea) {
        // 计算多边形中心点
        const center = this.calculatePolygonCenter(positions);

        this.measureDataSource.entities.add({
            position: center,
            label: {
                text: formattedArea,
                font: '14pt sans-serif',
                fillColor: Cesium.Color.CYAN,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
                backgroundColor: Cesium.Color.BLACK.withAlpha(0.7),
                backgroundPadding: new Cesium.Cartesian2(8, 4)
            }
        });
    }

    /**
     * 添加高度线和标签
     */
    addHeightLine(positions, heightDiff, distance) {
        // 添加连接线
        this.measureDataSource.entities.add({
            polyline: {
                positions: positions,
                width: 3,
                material: Cesium.Color.RED,
                depthFailMaterial: Cesium.Color.RED.withAlpha(0.5)
            }
        });

        // 添加高度标签
        const midpoint = Cesium.Cartesian3.midpoint(positions[0], positions[1], new Cesium.Cartesian3());
        this.measureDataSource.entities.add({
            position: midpoint,
            label: {
                text: `高度差: ${heightDiff.toFixed(2)}米\n距离: ${this.formatDistance(distance)}`,
                font: '12pt sans-serif',
                fillColor: Cesium.Color.RED,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
                backgroundColor: Cesium.Color.BLACK.withAlpha(0.7),
                backgroundPadding: new Cesium.Cartesian2(8, 4)
            }
        });
    }

    /**
     * 计算高度差
     */
    calculateHeightDifference(position1, position2) {
        const cartographic1 = Cesium.Cartographic.fromCartesian(position1);
        const cartographic2 = Cesium.Cartographic.fromCartesian(position2);
        return cartographic2.height - cartographic1.height;
    }

    /**
     * 计算多边形中心点
     */
    calculatePolygonCenter(positions) {
        let x = 0, y = 0, z = 0;
        positions.forEach(pos => {
            x += pos.x;
            y += pos.y;
            z += pos.z;
        });
        return new Cesium.Cartesian3(x / positions.length, y / positions.length, z / positions.length);
    }

    /**
     * 计算多边形面积（备用方法）
     */
    calculatePolygonArea(positions) {
        // 简化的面积计算，实际项目中建议使用更精确的算法
        let area = 0;
        const n = positions.length;

        for (let i = 0; i < n; i++) {
            const j = (i + 1) % n;
            const cart1 = Cesium.Cartographic.fromCartesian(positions[i]);
            const cart2 = Cesium.Cartographic.fromCartesian(positions[j]);
            area += cart1.longitude * cart2.latitude - cart2.longitude * cart1.latitude;
        }

        return Math.abs(area) * 6378137 * 6378137 / 2; // 近似计算
    }

    /**
     * 显示工具提示
     */
    updateTooltip(screenPosition, text) {
        // 这里可以实现自定义的tooltip显示
        if (!this.tooltipElement) {
            this.tooltipElement = document.createElement('div');
            this.tooltipElement.className = 'measure-tooltip';
            this.tooltipElement.style.cssText = `
                position: absolute;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
                font-size: 12px;
                pointer-events: none;
                z-index: 10000;
            `;
            document.body.appendChild(this.tooltipElement);
        }

        this.tooltipElement.innerHTML = text;
        this.tooltipElement.style.left = screenPosition.x + 10 + 'px';
        this.tooltipElement.style.top = screenPosition.y - 30 + 'px';
        this.tooltipElement.style.display = 'block';
    }

    /**
     * 隐藏工具提示
     */
    hideTooltip() {
        if (this.tooltipElement) {
            this.tooltipElement.style.display = 'none';
        }
    }

    /**
     * 显示通知
     */
    showNotification(title, message, type = 'info') {
        if (window.showNotification) {
            window.showNotification(title, message, type);
        } else {
            console.log(`${title}: ${message}`);
        }
    }

    /**
     * 销毁模块
     */
    destroy() {
        this.clearAll();
        this.viewer.dataSources.remove(this.measureDataSource);

        // 清理事件监听器
        this.handlers.forEach(handler => handler.destroy());
        this.handlers.clear();

        // 清理tooltip
        if (this.tooltipElement) {
            document.body.removeChild(this.tooltipElement);
        }
    }
}
