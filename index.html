<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子沙盘展示</title>
    
    <!-- Cesium CDN -->
    <link href="node_modules/cesium/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <link rel="stylesheet" href="src/features/布局/坐标导航/navigation-combined.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/title.css">
    <!-- Widget样式已移除 -->
    
    <!-- Font Awesome - 本地化 -->
    <link rel="stylesheet" href="node_modules/@fortawesome/fontawesome-free/css/all.min.css">

    <!-- JavaScript -->
    <script src="node_modules/cesium/Build/Cesium/Cesium.js"></script>
    <script src="node_modules/@turf/turf/turf.min.js"></script>
    <script src="node_modules/echarts/dist/echarts.min.js"></script>
    <!-- 可选：PDF生成库 - 保留CDN，按需加载 -->
    <script async src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <script src="src/js/cesium.js"></script>
    <script src="src/features/布局/天空盒/SkyBoxManager.js"></script>
    <script src="src/features/布局/坐标导航/CesiumNavigation.umd.js"></script>
    <script src="src/features/布局/坐标导航/CoordinateDisplay.js"></script>
</head>
<body>
    <div class="title-container">
        <img src="src/images/svg/title.svg" alt="标题">
        <div class="title-text">电子沙盘</div>
    </div>
    <div id="cesiumContainer"></div>
    <div id="notificationContainer" class="notification-container"></div>
    <div id="svg-container"></div>

    <!-- 右下角工具栏 -->
    <div class="toolbar-container">
        <div class="toolbar-item">
            <div class="toolbar-button" data-popup="measure" title="测量与分析工具类">
                <i class="fas fa-ruler-combined"></i>
            </div>
            <div class="toolbar-popup" id="popup-measure">
                <div class="popup-header">
                    <h3 class="popup-title">
                        <i class="fas fa-ruler-combined"></i>
                        测量与分析工具
                    </h3>
                </div>
                <div class="popup-content" id="measure-content">
                    <iframe
                        src="src/modules/measure/measure-tool.html"
                        width="100%"
                        height="100%"
                        frameborder="0"
                        style="border: none; background: transparent;"
                        onload="handleIframeLoad(this)">
                    </iframe>
                </div>
            </div>
        </div>
    </div>
    </div>
    <script>
        function showNotification(title, message, type = 'info', duration = 4000) {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-title">${title}</div>
                    <div>${message}</div>
                </div>
            `;
            container.appendChild(notification);
            setTimeout(() => {
                notification.classList.add('hide');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, duration);
        }

        // iframe加载完成处理
        function handleIframeLoad(iframe) {
            console.log('✅ iframe加载完成:', iframe.src);

            // 确保iframe可以访问父窗口的viewer
            try {
                if (iframe.contentWindow && window.viewer) {
                    // 可以在这里进行额外的初始化
                    console.log('🔗 iframe与主窗口连接成功');
                }
            } catch (error) {
                console.warn('iframe通信设置失败:', error);
            }
        }

        // 工具栏弹窗控制
        function initToolbarPopups() {
            const toolbarButtons = document.querySelectorAll('.toolbar-button[data-popup]');
            let currentActivePopup = null;

            toolbarButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();

                    const popupId = this.getAttribute('data-popup');
                    const popup = document.getElementById(`popup-${popupId}`);

                    // 如果点击的是当前激活的按钮，则关闭弹窗
                    if (currentActivePopup === popup && popup.classList.contains('show')) {
                        closeAllPopups();
                        return;
                    }

                    // 关闭所有弹窗
                    closeAllPopups();

                    // 显示当前弹窗
                    if (popup) {
                        popup.classList.add('show');
                        this.classList.add('active');
                        currentActivePopup = popup;
                    }
                });
            });

            // 点击其他地方关闭弹窗
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.toolbar-container')) {
                    closeAllPopups();
                }
            });

            function closeAllPopups() {
                document.querySelectorAll('.toolbar-popup').forEach(popup => {
                    popup.classList.remove('show');
                });
                document.querySelectorAll('.toolbar-button').forEach(button => {
                    button.classList.remove('active');
                });
                currentActivePopup = null;
            }
        }

        // 等待页面加载完成
        window.onload = function() {
            try {
                // 初始化Cesium
                const viewer = initCesium();

                // 确保viewer对象全局可访问（供iframe使用）
                window.viewer = viewer;

                // 初始化坐标显示功能
                window.coordinateDisplay = new CoordinateDisplay(viewer);

                // 初始化工具栏弹窗
                initToolbarPopups();

                // 完成系统初始化
                console.log('✅ 系统初始化完成');

                // 加载SVG图标
                fetch('src/images/svg/icons.svg')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.text();
                    })
                    .then(svgContent => {
                        document.getElementById('svg-container').innerHTML = svgContent;
                        console.log('✅ SVG图标加载完成');
                    })
                    .catch(error => {
                        console.warn('SVG图标加载失败:', error);
                        // 创建一个简单的占位符SVG
                        document.getElementById('svg-container').innerHTML = `
                            <svg style="display: none;">
                                <defs>
                                    <symbol id="icon-placeholder" viewBox="0 0 24 24">
                                        <circle cx="12" cy="12" r="10" fill="#40E0FF" opacity="0.5"/>
                                    </symbol>
                                </defs>
                            </svg>
                        `;
                    });

            } catch (error) {
                console.error('初始化失败:', error);
            }
        };
    </script>
</body>
</html>
