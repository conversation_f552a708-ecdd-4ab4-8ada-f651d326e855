/**
 * 测量工具UI组件
 * 现代化的测量工具界面
 */

export class MeasureUI {
    constructor(container, measureModule) {
        this.container = container;
        this.measureModule = measureModule;
        this.activeUnit = {
            distance: 'auto',
            area: 'auto'
        };
        
        this.init();
    }

    /**
     * 初始化UI
     */
    init() {
        this.createUI();
        this.bindEvents();
    }

    /**
     * 创建UI界面
     */
    createUI() {
        this.container.innerHTML = `
            <div class="measure-container">
                <!-- 工具按钮区域 -->
                <div class="measure-tools">
                    <div class="tool-grid">
                        <div class="tool-item" data-tool="distance">
                            <div class="tool-icon">
                                <i class="fas fa-ruler"></i>
                            </div>
                            <span class="tool-label">距离测量</span>
                        </div>
                        
                        <div class="tool-item" data-tool="distance-ground">
                            <div class="tool-icon">
                                <i class="fas fa-route"></i>
                            </div>
                            <span class="tool-label">贴地距离</span>
                        </div>
                        
                        <div class="tool-item" data-tool="area">
                            <div class="tool-icon">
                                <i class="fas fa-draw-polygon"></i>
                            </div>
                            <span class="tool-label">面积测量</span>
                        </div>
                        
                        <div class="tool-item" data-tool="area-ground">
                            <div class="tool-icon">
                                <i class="fas fa-vector-square"></i>
                            </div>
                            <span class="tool-label">贴地面积</span>
                        </div>
                        
                        <div class="tool-item" data-tool="height">
                            <div class="tool-icon">
                                <i class="fas fa-arrows-alt-v"></i>
                            </div>
                            <span class="tool-label">高度差</span>
                        </div>
                        
                        <div class="tool-item" data-tool="angle">
                            <div class="tool-icon">
                                <i class="fas fa-angle-up"></i>
                            </div>
                            <span class="tool-label">角度测量</span>
                        </div>
                    </div>
                </div>

                <!-- 单位选择区域 -->
                <div class="measure-units">
                    <div class="unit-group">
                        <label class="unit-label">距离单位:</label>
                        <select class="unit-select" data-type="distance">
                            <option value="auto">自动</option>
                            <option value="m">米</option>
                            <option value="km">公里</option>
                            <option value="mile">海里</option>
                        </select>
                    </div>
                    
                    <div class="unit-group">
                        <label class="unit-label">面积单位:</label>
                        <select class="unit-select" data-type="area">
                            <option value="auto">自动</option>
                            <option value="m">平方米</option>
                            <option value="km">平方公里</option>
                            <option value="ha">公顷</option>
                            <option value="mu">亩</option>
                        </select>
                    </div>
                </div>

                <!-- 结果显示区域 -->
                <div class="measure-results">
                    <div class="result-header">
                        <span class="result-title">测量结果</span>
                        <button class="clear-btn" title="清除所有测量">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="result-content">
                        <div class="result-placeholder">
                            点击上方工具开始测量
                        </div>
                    </div>
                </div>

                <!-- 操作提示 -->
                <div class="measure-tips">
                    <div class="tip-content">
                        <i class="fas fa-info-circle"></i>
                        <span class="tip-text">选择测量工具，在地图上点击开始测量</span>
                    </div>
                </div>
            </div>
        `;

        this.addStyles();
    }

    /**
     * 添加样式
     */
    addStyles() {
        if (document.getElementById('measure-ui-styles')) return;

        const style = document.createElement('style');
        style.id = 'measure-ui-styles';
        style.textContent = `
            .measure-container {
                padding: 16px;
                height: 100%;
                display: flex;
                flex-direction: column;
                gap: 16px;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }

            .measure-tools {
                flex-shrink: 0;
            }

            .tool-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }

            .tool-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 12px 8px;
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.2s ease;
                min-height: 70px;
                justify-content: center;
            }

            .tool-item:hover {
                background: rgba(64, 224, 255, 0.1);
                border-color: rgba(64, 224, 255, 0.3);
                transform: translateY(-1px);
            }

            .tool-item.active {
                background: rgba(64, 224, 255, 0.2);
                border-color: rgba(64, 224, 255, 0.5);
                color: #40E0FF;
            }

            .tool-icon {
                font-size: 20px;
                margin-bottom: 6px;
                color: #40E0FF;
            }

            .tool-label {
                font-size: 11px;
                text-align: center;
                color: #ffffff;
                line-height: 1.2;
            }

            .measure-units {
                flex-shrink: 0;
                display: flex;
                flex-direction: column;
                gap: 8px;
                padding: 12px;
                background: rgba(255, 255, 255, 0.03);
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }

            .unit-group {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .unit-label {
                font-size: 12px;
                color: #ffffff;
                min-width: 60px;
            }

            .unit-select {
                flex: 1;
                padding: 4px 8px;
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                color: #ffffff;
                font-size: 12px;
            }

            .unit-select option {
                background: #2a2a2a;
                color: #ffffff;
            }

            .measure-results {
                flex: 1;
                display: flex;
                flex-direction: column;
                background: rgba(255, 255, 255, 0.03);
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                overflow: hidden;
            }

            .result-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px;
                background: rgba(255, 255, 255, 0.05);
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            .result-title {
                font-size: 14px;
                font-weight: 500;
                color: #40E0FF;
            }

            .clear-btn {
                background: none;
                border: none;
                color: #ff6b6b;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                transition: background 0.2s ease;
            }

            .clear-btn:hover {
                background: rgba(255, 107, 107, 0.1);
            }

            .result-content {
                flex: 1;
                padding: 12px;
                overflow-y: auto;
            }

            .result-placeholder {
                text-align: center;
                color: rgba(255, 255, 255, 0.5);
                font-size: 12px;
                padding: 20px;
            }

            .result-item {
                padding: 8px 12px;
                margin-bottom: 8px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 6px;
                border-left: 3px solid #40E0FF;
            }

            .result-type {
                font-size: 11px;
                color: #40E0FF;
                margin-bottom: 4px;
            }

            .result-value {
                font-size: 13px;
                color: #ffffff;
                font-weight: 500;
            }

            .measure-tips {
                flex-shrink: 0;
                padding: 8px 12px;
                background: rgba(64, 224, 255, 0.1);
                border-radius: 6px;
                border: 1px solid rgba(64, 224, 255, 0.2);
            }

            .tip-content {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 11px;
                color: #40E0FF;
            }

            .tip-content i {
                font-size: 12px;
            }

            /* 滚动条样式 */
            .result-content::-webkit-scrollbar {
                width: 4px;
            }

            .result-content::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
            }

            .result-content::-webkit-scrollbar-thumb {
                background: rgba(64, 224, 255, 0.5);
                border-radius: 2px;
            }

            .result-content::-webkit-scrollbar-thumb:hover {
                background: rgba(64, 224, 255, 0.7);
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 工具按钮点击事件
        this.container.addEventListener('click', (e) => {
            const toolItem = e.target.closest('.tool-item');
            if (toolItem) {
                this.handleToolClick(toolItem);
            }

            // 清除按钮
            if (e.target.closest('.clear-btn')) {
                this.clearAllMeasurements();
            }
        });

        // 单位选择变化事件
        this.container.addEventListener('change', (e) => {
            if (e.target.classList.contains('unit-select')) {
                const type = e.target.dataset.type;
                const value = e.target.value;
                this.activeUnit[type] = value;
            }
        });
    }

    /**
     * 处理工具点击
     */
    async handleToolClick(toolItem) {
        const tool = toolItem.dataset.tool;
        
        // 更新UI状态
        this.setActiveTool(toolItem);
        
        // 更新提示
        this.updateTip(this.getToolTip(tool));

        try {
            let result;
            switch (tool) {
                case 'distance':
                    result = await this.measureModule.measureDistance({
                        unit: this.activeUnit.distance,
                        clampToGround: false
                    });
                    break;
                case 'distance-ground':
                    result = await this.measureModule.measureDistance({
                        unit: this.activeUnit.distance,
                        clampToGround: true
                    });
                    break;
                case 'area':
                    result = await this.measureModule.measureArea({
                        unit: this.activeUnit.area,
                        clampToGround: false
                    });
                    break;
                case 'area-ground':
                    result = await this.measureModule.measureArea({
                        unit: this.activeUnit.area,
                        clampToGround: true
                    });
                    break;
                case 'height':
                    result = await this.measureModule.measureHeight();
                    break;
                default:
                    console.warn('未实现的测量工具:', tool);
                    return;
            }

            // 显示结果
            this.addResult(result);
            
        } catch (error) {
            console.error('测量失败:', error);
        } finally {
            // 重置UI状态
            this.clearActiveTool();
            this.updateTip('选择测量工具，在地图上点击开始测量');
        }
    }

    /**
     * 设置激活工具
     */
    setActiveTool(toolItem) {
        // 清除之前的激活状态
        this.container.querySelectorAll('.tool-item.active').forEach(item => {
            item.classList.remove('active');
        });
        
        // 设置当前激活状态
        toolItem.classList.add('active');
    }

    /**
     * 清除激活工具
     */
    clearActiveTool() {
        this.container.querySelectorAll('.tool-item.active').forEach(item => {
            item.classList.remove('active');
        });
    }

    /**
     * 获取工具提示
     */
    getToolTip(tool) {
        const tips = {
            'distance': '在地图上点击两个点测量空间距离',
            'distance-ground': '在地图上点击两个点测量贴地距离',
            'area': '在地图上点击多个点，双击结束面积测量',
            'area-ground': '在地图上点击多个点，双击结束贴地面积测量',
            'height': '在地图上点击两个点测量高度差'
        };
        return tips[tool] || '开始测量';
    }

    /**
     * 更新提示
     */
    updateTip(text) {
        const tipText = this.container.querySelector('.tip-text');
        if (tipText) {
            tipText.textContent = text;
        }
    }

    /**
     * 添加测量结果
     */
    addResult(result) {
        const resultContent = this.container.querySelector('.result-content');
        const placeholder = resultContent.querySelector('.result-placeholder');
        
        if (placeholder) {
            placeholder.remove();
        }

        const resultItem = document.createElement('div');
        resultItem.className = 'result-item';
        
        let typeText, valueText;
        switch (result.type) {
            case 'distance':
                typeText = '距离测量';
                valueText = result.formattedDistance;
                break;
            case 'area':
                typeText = '面积测量';
                valueText = result.formattedArea;
                break;
            case 'height':
                typeText = '高度差测量';
                valueText = `${result.heightDifference.toFixed(2)}米`;
                break;
            default:
                typeText = '测量结果';
                valueText = '未知';
        }

        resultItem.innerHTML = `
            <div class="result-type">${typeText}</div>
            <div class="result-value">${valueText}</div>
        `;

        resultContent.appendChild(resultItem);
        
        // 滚动到底部
        resultContent.scrollTop = resultContent.scrollHeight;
    }

    /**
     * 清除所有测量结果
     */
    clearAllMeasurements() {
        this.measureModule.clearAll();
        
        const resultContent = this.container.querySelector('.result-content');
        resultContent.innerHTML = `
            <div class="result-placeholder">
                点击上方工具开始测量
            </div>
        `;
        
        this.clearActiveTool();
        this.updateTip('选择测量工具，在地图上点击开始测量');
    }

    /**
     * 销毁UI
     */
    destroy() {
        // 移除样式
        const styleElement = document.getElementById('measure-ui-styles');
        if (styleElement) {
            styleElement.remove();
        }
        
        // 清空容器
        this.container.innerHTML = '';
    }
}
