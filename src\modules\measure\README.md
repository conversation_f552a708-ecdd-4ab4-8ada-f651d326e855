# 测量分析模块

## 📏 功能特性

### 🎯 核心功能
- **距离测量** - 空间直线距离和贴地距离
- **面积测量** - 投影面积和贴地面积  
- **高度差测量** - 两点间的高度差计算
- **角度测量** - 三点角度计算（待实现）

### 🎨 UI特性
- **现代化界面** - 基于ES6模块的组件化设计
- **响应式布局** - 适配不同尺寸的弹窗容器
- **实时反馈** - 鼠标移动时的动态预览
- **结果管理** - 测量结果的显示和清除

### 🔧 技术特点
- **ES6模块化** - 完全基于ES6 import/export
- **高度解耦** - 逻辑层和UI层分离
- **异步操作** - 基于Promise的测量流程
- **事件驱动** - 完善的事件处理机制

## 🚀 使用方法

### 基础用法

```javascript
// 动态导入模块
const { createMeasureTool } = await import('./src/modules/measure/index.js');

// 创建测量工具
const measureManager = await createMeasureTool(viewer, container);

// 激活工具
measureManager.activate();

// 禁用工具
measureManager.disable();

// 清除结果
measureManager.clearAll();

// 销毁工具
measureManager.destroy();
```

### 高级用法

```javascript
// 获取测量模块进行自定义操作
const measureModule = measureManager.getMeasureModule();

// 自定义距离测量
const result = await measureModule.measureDistance({
    clampToGround: true,
    unit: 'km',
    color: Cesium.Color.RED
});

// 自定义面积测量
const areaResult = await measureModule.measureArea({
    clampToGround: false,
    unit: 'ha',
    color: Cesium.Color.BLUE.withAlpha(0.5)
});
```

## 📁 文件结构

```
src/modules/measure/
├── index.js           # 模块入口文件
├── MeasureModule.js   # 核心测量逻辑
├── MeasureUI.js       # UI界面组件
└── README.md          # 说明文档
```

## 🎨 样式定制

测量工具的样式完全可定制，主要CSS类：

- `.measure-container` - 主容器
- `.tool-grid` - 工具按钮网格
- `.tool-item` - 单个工具按钮
- `.measure-results` - 结果显示区域
- `.result-item` - 单个结果项

## 🔌 集成方式

### 1. 弹窗集成（推荐）
```html
<div class="toolbar-popup" id="popup-measure">
    <div class="popup-content" id="measure-content">
        <!-- 测量工具将在这里动态加载 -->
    </div>
</div>
```

### 2. 独立页面集成
```html
<div id="measure-container" style="width: 320px; height: 400px;">
    <!-- 测量工具容器 -->
</div>
```

### 3. iframe集成
```html
<iframe src="measure-tool.html" width="320" height="400"></iframe>
```

## 🎯 配置选项

### 测量配置
```javascript
{
    clampToGround: false,    // 是否贴地
    unit: 'auto',           // 单位：auto/m/km/mile/ha/mu
    color: Cesium.Color.YELLOW,  // 颜色
    outlineColor: Cesium.Color.WHITE  // 边框颜色
}
```

### UI配置
```javascript
{
    showUnits: true,        // 显示单位选择
    showResults: true,      // 显示结果面板
    showTips: true,         // 显示操作提示
    gridColumns: 2          // 工具按钮列数
}
```

## 🐛 已知问题

1. **turf.js依赖** - 面积计算依赖turf.js库
2. **地形精度** - 贴地测量精度取决于地形数据质量
3. **性能优化** - 大量测量结果可能影响性能

## 🔄 更新日志

### v1.0.0 (2024-08-15)
- ✅ 基础测量功能实现
- ✅ 现代化UI界面
- ✅ ES6模块化架构
- ✅ 弹窗集成支持

### 待实现功能
- 🔲 角度测量
- 🔲 坐标测量  
- 🔲 剖面分析
- 🔲 测量结果导出
- 🔲 测量样式自定义

## 📞 技术支持

如有问题或建议，请联系开发团队。
