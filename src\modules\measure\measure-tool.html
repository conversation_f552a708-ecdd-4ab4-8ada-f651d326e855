<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测量与分析工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: transparent;
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .measure-container {
            padding: 16px;
            height: 100%;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .measure-tools {
            flex-shrink: 0;
        }

        .tool-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .tool-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 8px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: 70px;
            justify-content: center;
        }

        .tool-item:hover {
            background: rgba(64, 224, 255, 0.1);
            border-color: rgba(64, 224, 255, 0.3);
            transform: translateY(-1px);
        }

        .tool-item.active {
            background: rgba(64, 224, 255, 0.2);
            border-color: rgba(64, 224, 255, 0.5);
            color: #40E0FF;
        }

        .tool-icon {
            font-size: 20px;
            margin-bottom: 6px;
            color: #40E0FF;
        }

        .tool-label {
            font-size: 11px;
            text-align: center;
            color: #ffffff;
            line-height: 1.2;
        }

        .measure-units {
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .unit-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .unit-label {
            font-size: 12px;
            color: #ffffff;
            min-width: 60px;
        }

        .unit-select {
            flex: 1;
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            color: #ffffff;
            font-size: 12px;
        }

        .unit-select option {
            background: #2a2a2a;
            color: #ffffff;
        }

        .measure-results {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            overflow: hidden;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .result-title {
            font-size: 14px;
            font-weight: 500;
            color: #40E0FF;
        }

        .clear-btn {
            background: none;
            border: none;
            color: #ff6b6b;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .clear-btn:hover {
            background: rgba(255, 107, 107, 0.1);
        }

        .result-content {
            flex: 1;
            padding: 12px;
            overflow-y: auto;
        }

        .result-placeholder {
            text-align: center;
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
            padding: 20px;
        }

        .result-item {
            padding: 8px 12px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            border-left: 3px solid #40E0FF;
        }

        .result-type {
            font-size: 11px;
            color: #40E0FF;
            margin-bottom: 4px;
        }

        .result-value {
            font-size: 13px;
            color: #ffffff;
            font-weight: 500;
        }

        .measure-tips {
            flex-shrink: 0;
            padding: 8px 12px;
            background: rgba(64, 224, 255, 0.1);
            border-radius: 6px;
            border: 1px solid rgba(64, 224, 255, 0.2);
        }

        .tip-content {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 11px;
            color: #40E0FF;
        }

        .tip-content i {
            font-size: 12px;
        }

        /* 滚动条样式 */
        .result-content::-webkit-scrollbar {
            width: 4px;
        }

        .result-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        .result-content::-webkit-scrollbar-thumb {
            background: rgba(64, 224, 255, 0.5);
            border-radius: 2px;
        }

        .result-content::-webkit-scrollbar-thumb:hover {
            background: rgba(64, 224, 255, 0.7);
        }

        /* 工具提示样式 */
        .measure-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 10000;
            display: none;
        }
    </style>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="../../node_modules/@fortawesome/fontawesome-free/css/all.min.css">
</head>
<body>
    <div class="measure-container">
        <!-- 工具按钮区域 -->
        <div class="measure-tools">
            <div class="tool-grid">
                <div class="tool-item" data-tool="distance">
                    <div class="tool-icon">
                        <i class="fas fa-ruler"></i>
                    </div>
                    <span class="tool-label">距离测量</span>
                </div>
                
                <div class="tool-item" data-tool="distance-ground">
                    <div class="tool-icon">
                        <i class="fas fa-route"></i>
                    </div>
                    <span class="tool-label">贴地距离</span>
                </div>
                
                <div class="tool-item" data-tool="area">
                    <div class="tool-icon">
                        <i class="fas fa-draw-polygon"></i>
                    </div>
                    <span class="tool-label">面积测量</span>
                </div>
                
                <div class="tool-item" data-tool="area-ground">
                    <div class="tool-icon">
                        <i class="fas fa-vector-square"></i>
                    </div>
                    <span class="tool-label">贴地面积</span>
                </div>
                
                <div class="tool-item" data-tool="height">
                    <div class="tool-icon">
                        <i class="fas fa-arrows-alt-v"></i>
                    </div>
                    <span class="tool-label">高度差</span>
                </div>
                
                <div class="tool-item" data-tool="angle">
                    <div class="tool-icon">
                        <i class="fas fa-angle-up"></i>
                    </div>
                    <span class="tool-label">角度测量</span>
                </div>
            </div>
        </div>

        <!-- 单位选择区域 -->
        <div class="measure-units">
            <div class="unit-group">
                <label class="unit-label">距离单位:</label>
                <select class="unit-select" data-type="distance">
                    <option value="auto">自动</option>
                    <option value="m">米</option>
                    <option value="km">公里</option>
                    <option value="mile">海里</option>
                </select>
            </div>
            
            <div class="unit-group">
                <label class="unit-label">面积单位:</label>
                <select class="unit-select" data-type="area">
                    <option value="auto">自动</option>
                    <option value="m">平方米</option>
                    <option value="km">平方公里</option>
                    <option value="ha">公顷</option>
                    <option value="mu">亩</option>
                </select>
            </div>
        </div>

        <!-- 结果显示区域 -->
        <div class="measure-results">
            <div class="result-header">
                <span class="result-title">测量结果</span>
                <button class="clear-btn" title="清除所有测量">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="result-content">
                <div class="result-placeholder">
                    点击上方工具开始测量
                </div>
            </div>
        </div>

        <!-- 操作提示 -->
        <div class="measure-tips">
            <div class="tip-content">
                <i class="fas fa-info-circle"></i>
                <span class="tip-text">选择测量工具，在地图上点击开始测量</span>
            </div>
        </div>
    </div>

    <script>
        // 获取父窗口的viewer对象
        let viewer = null;
        let measureModule = null;
        let activeUnit = {
            distance: 'auto',
            area: 'auto'
        };

        // 初始化
        window.addEventListener('load', function() {
            // 绑定事件
            bindEvents();

            // 尝试获取父窗口的viewer（带重试机制）
            tryGetViewer();
        });

        // 尝试获取viewer的函数（带重试机制）
        function tryGetViewer(retryCount = 0) {
            const maxRetries = 10;
            const retryDelay = 500;

            try {
                // 多种方式尝试获取viewer
                viewer = window.parent.viewer ||
                         window.parent.window.viewer ||
                         window.top.viewer ||
                         window.top.window.viewer;

                if (viewer && viewer.scene) {
                    console.log('✅ 成功获取Cesium Viewer');
                    initMeasureModule();
                    return;
                }
            } catch (error) {
                console.warn('尝试获取viewer失败:', error);
            }

            // 如果还没有获取到viewer且未超过重试次数，则继续重试
            if (retryCount < maxRetries) {
                console.log(`⏳ 正在重试获取Cesium Viewer... (${retryCount + 1}/${maxRetries})`);
                setTimeout(() => {
                    tryGetViewer(retryCount + 1);
                }, retryDelay);
            } else {
                console.error('❌ 无法获取Cesium Viewer，请确保父页面已正确初始化Cesium');
                showError('无法连接到地图引擎，请刷新页面重试');
            }
        }

        // 显示错误信息
        function showError(message) {
            const container = document.querySelector('.measure-container');
            container.innerHTML = `
                <div style="
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    text-align: center;
                    color: #ff6b6b;
                    padding: 20px;
                ">
                    <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 16px;"></i>
                    <h3 style="margin-bottom: 8px;">连接失败</h3>
                    <p style="font-size: 14px; opacity: 0.8;">${message}</p>
                    <button onclick="location.reload()" style="
                        margin-top: 16px;
                        padding: 8px 16px;
                        background: #ff6b6b;
                        border: none;
                        border-radius: 4px;
                        color: white;
                        cursor: pointer;
                    ">重新加载</button>
                </div>
            `;
        }

        // 初始化测量模块
        async function initMeasureModule() {
            try {
                // 动态导入测量模块
                const { MeasureModule } = await import('../modules/measure/MeasureModule.js');
                measureModule = new MeasureModule(viewer);
                console.log('✅ 测量模块初始化成功');
            } catch (error) {
                console.error('❌ 测量模块初始化失败:', error);
                // 如果模块导入失败，使用内置的简化测量功能
                initSimpleMeasure();
            }
        }

        // 简化的测量功能（备用方案）
        function initSimpleMeasure() {
            console.log('🔄 使用内置简化测量功能');
            measureModule = {
                measureDistance: function(options) {
                    return new Promise((resolve) => {
                        console.log('简化距离测量功能');
                        resolve({ type: 'distance', formattedDistance: '功能开发中...' });
                    });
                },
                measureArea: function(options) {
                    return new Promise((resolve) => {
                        console.log('简化面积测量功能');
                        resolve({ type: 'area', formattedArea: '功能开发中...' });
                    });
                },
                measureHeight: function(options) {
                    return new Promise((resolve) => {
                        console.log('简化高度测量功能');
                        resolve({ type: 'height', heightDifference: 0 });
                    });
                },
                clearAll: function() {
                    console.log('清除测量结果');
                }
            };
        }

        // 绑定事件
        function bindEvents() {
            // 工具按钮点击事件
            document.addEventListener('click', async (e) => {
                const toolItem = e.target.closest('.tool-item');
                if (toolItem) {
                    await handleToolClick(toolItem);
                }

                // 清除按钮
                if (e.target.closest('.clear-btn')) {
                    clearAllMeasurements();
                }
            });

            // 单位选择变化事件
            document.addEventListener('change', (e) => {
                if (e.target.classList.contains('unit-select')) {
                    const type = e.target.dataset.type;
                    const value = e.target.value;
                    activeUnit[type] = value;
                }
            });
        }

        // 处理工具点击
        async function handleToolClick(toolItem) {
            if (!measureModule) {
                console.error('测量模块未初始化');
                return;
            }

            const tool = toolItem.dataset.tool;
            
            // 更新UI状态
            setActiveTool(toolItem);
            
            // 更新提示
            updateTip(getToolTip(tool));

            try {
                let result;
                switch (tool) {
                    case 'distance':
                        result = await measureModule.measureDistance({
                            unit: activeUnit.distance,
                            clampToGround: false
                        });
                        break;
                    case 'distance-ground':
                        result = await measureModule.measureDistance({
                            unit: activeUnit.distance,
                            clampToGround: true
                        });
                        break;
                    case 'area':
                        result = await measureModule.measureArea({
                            unit: activeUnit.area,
                            clampToGround: false
                        });
                        break;
                    case 'area-ground':
                        result = await measureModule.measureArea({
                            unit: activeUnit.area,
                            clampToGround: true
                        });
                        break;
                    case 'height':
                        result = await measureModule.measureHeight();
                        break;
                    case 'angle':
                        console.log('角度测量功能待实现');
                        return;
                    default:
                        console.warn('未实现的测量工具:', tool);
                        return;
                }

                // 显示结果
                addResult(result);
                
            } catch (error) {
                console.error('测量失败:', error);
            } finally {
                // 重置UI状态
                clearActiveTool();
                updateTip('选择测量工具，在地图上点击开始测量');
            }
        }

        // 设置激活工具
        function setActiveTool(toolItem) {
            document.querySelectorAll('.tool-item.active').forEach(item => {
                item.classList.remove('active');
            });
            toolItem.classList.add('active');
        }

        // 清除激活工具
        function clearActiveTool() {
            document.querySelectorAll('.tool-item.active').forEach(item => {
                item.classList.remove('active');
            });
        }

        // 获取工具提示
        function getToolTip(tool) {
            const tips = {
                'distance': '在地图上点击两个点测量空间距离',
                'distance-ground': '在地图上点击两个点测量贴地距离',
                'area': '在地图上点击多个点，双击结束面积测量',
                'area-ground': '在地图上点击多个点，双击结束贴地面积测量',
                'height': '在地图上点击两个点测量高度差',
                'angle': '在地图上点击三个点测量角度'
            };
            return tips[tool] || '开始测量';
        }

        // 更新提示
        function updateTip(text) {
            const tipText = document.querySelector('.tip-text');
            if (tipText) {
                tipText.textContent = text;
            }
        }

        // 添加测量结果
        function addResult(result) {
            const resultContent = document.querySelector('.result-content');
            const placeholder = resultContent.querySelector('.result-placeholder');
            
            if (placeholder) {
                placeholder.remove();
            }

            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';
            
            let typeText, valueText;
            switch (result.type) {
                case 'distance':
                    typeText = '距离测量';
                    valueText = result.formattedDistance;
                    break;
                case 'area':
                    typeText = '面积测量';
                    valueText = result.formattedArea;
                    break;
                case 'height':
                    typeText = '高度差测量';
                    valueText = `${result.heightDifference.toFixed(2)}米`;
                    break;
                default:
                    typeText = '测量结果';
                    valueText = '未知';
            }

            resultItem.innerHTML = `
                <div class="result-type">${typeText}</div>
                <div class="result-value">${valueText}</div>
            `;

            resultContent.appendChild(resultItem);
            
            // 滚动到底部
            resultContent.scrollTop = resultContent.scrollHeight;
        }

        // 清除所有测量结果
        function clearAllMeasurements() {
            if (measureModule) {
                measureModule.clearAll();
            }
            
            const resultContent = document.querySelector('.result-content');
            resultContent.innerHTML = `
                <div class="result-placeholder">
                    点击上方工具开始测量
                </div>
            `;
            
            clearActiveTool();
            updateTip('选择测量工具，在地图上点击开始测量');
        }
    </script>
</body>
</html>
